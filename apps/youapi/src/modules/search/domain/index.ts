/**
 * Search domain service based on typesense
 *
 * Initialize:
 * Define typesense collections(schema.ts)
 *
 * Index:
 * Fire search event(events.ts) -> Convert entity to search document(document.ts) -> Index to typesense(index.ts)
 *
 * Search:
 * Build search query(query.ts) -> Query typesense(index.ts) -> Process search result(result.ts)
 *
 * @migration Migrated from Next.js to NestJS
 */

import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { getServicesEnv, type YouMindEnv } from '@repo/common';
import { groupBy } from 'lodash-es';
import { MultiSearchRequestSchema } from 'typesense/lib/Typesense/MultiSearch';
import type { SearchResultTypeEnum } from '@/common/types/chat.types';
import { ClickhouseService } from '@/infra/clickhouse';
import { ClickhouseTable } from '@/infra/clickhouse/enums';
import { TypesenseService } from '@/infra/typesense';
import { EmbeddingService } from '@/modules/ai/services/embedding.service';
import { SearchDocumentService } from '../services/search-document.service';
import {
  DEFAULT_SEARCH_CHUNK_FIELDS,
  DEFAULT_SEARCH_FIELDS,
  DEFAULT_SEMANTIC_DISTANCE,
  type EntityToBeIndexed,
  type SnipSearchDocument,
  type ThoughtSearchDocument,
  TypesenseCollection,
} from './definition';
import { SearchQueryService } from './query';
import { SearchResultDomainService } from './result';
import { SCHEMA_MAP } from './schema';
import { type LocalizedObject, restoreLocalizedFields, toLocalizedFieldNames } from './utils';

export type HybridSearchChunksParams = {
  query: string;
  userId: string;
  snipIds?: string[];
  thoughtIds?: string[];
  boardIds?: string[];
  boardGroupIds?: string[];
  maxResults?: number;
  threshold?: number;
  fields?: string[];
  enrichMetadata?: boolean;
  rerank?: boolean;
};

@Injectable()
export class SearchDomainService {
  private readonly logger = new Logger(SearchDomainService.name);

  constructor(
    private readonly typesenseService: TypesenseService,
    private readonly clickhouseService: ClickhouseService,
    @Inject(forwardRef(() => EmbeddingService))
    private readonly embeddingService: EmbeddingService,
    private readonly searchQueryService: SearchQueryService,
    @Inject(forwardRef(() => SearchResultDomainService))
    private readonly searchResultService: SearchResultDomainService,
    private readonly searchDocumentService: SearchDocumentService,
  ) {}

  private get typesense() {
    return this.typesenseService;
  }

  async search({
    query,
    userId,
    fields = DEFAULT_SEARCH_FIELDS,
    boardGroupIds,
    boardIds,
    snipIds,
    thoughtIds,
    searchOptions,
  }: {
    query: string;
    userId: string;
    fields?: string[];
    boardIds?: string[];
    boardGroupIds?: string[];
    snipIds?: string[];
    thoughtIds?: string[];
    searchOptions?: Partial<MultiSearchRequestSchema>;
  }) {
    try {
      const queries = await this.searchQueryService.buildSearchQueries({
        fields,
        userId,
        query,
        boardIds,
        snipIds,
        thoughtIds,
        boardGroupIds,
      });
      if (queries.length === 0) {
        return {
          hits: [],
          total: 0,
        };
      }

      const result = await this.performTypesenseSearch(queries, searchOptions);
      return result;
    } catch (error) {
      this.logger.warn(error);
      return {
        hits: [],
        total: 0,
      };
    }
  }

  async semanticSearchChunks({
    query,
    userId,
    snipIds,
    boardIds,
    boardGroupIds,
    thoughtIds,
    maxResults = 5,
    threshold = DEFAULT_SEMANTIC_DISTANCE,
    fields = DEFAULT_SEARCH_CHUNK_FIELDS,
  }: {
    query: string;
    userId: string;
    snipIds?: string[];
    thoughtIds?: string[];
    boardIds?: string[];
    boardGroupIds?: string[];
    maxResults?: number;
    threshold?: number;
    fields?: string[];
  }) {
    const finalQuery = query.replace(/<mark>/g, '"').replace(/<\/mark>/g, '"');
    try {
      const embeddings = await this.embeddingService.createEmbedding([finalQuery]);

      const result = await this.clickhouseService.searchChunksFromYoumindEntities({
        queryVector: embeddings[0],
        userId,
        threshold,
        maxResults,
        snipIds,
        thoughtIds,
        boardIds,
        boardGroupIds,
        fields,
      });

      return result;
    } catch (error) {
      this.logger.error(error, 'Error in semanticSearchChunks:');
      return [];
    }
  }

  async hybridSearchChunks({
    query,
    userId,
    snipIds,
    thoughtIds,
    boardIds,
    boardGroupIds,
    maxResults = 5,
    enrichMetadata = false,
    threshold = DEFAULT_SEMANTIC_DISTANCE,
    fields = DEFAULT_SEARCH_CHUNK_FIELDS,
    rerank = false,
  }: HybridSearchChunksParams) {
    // Remove possible <mark> added by LLM to perform a standard search,
    // but will rerank NERs if applicable
    // @see YOU-1469
    const finalQuery = query.replace(/<mark>/g, '').replace(/<\/mark>/g, '');

    const [semanticResult, fullTextResult] = await Promise.all([
      this.semanticSearchChunks({
        query: finalQuery,
        userId,
        snipIds,
        thoughtIds,
        boardIds,
        boardGroupIds,
        maxResults: maxResults * 2,
        threshold,
        fields,
      }),
      this.search({
        query: finalQuery,
        userId,
        fields,
        searchOptions: {
          highlight_affix_num_tokens: 200,
          highlight_start_tag: '',
          highlight_end_tag: '',
        },
        boardIds,
        snipIds,
        thoughtIds,
        boardGroupIds,
      }),
    ]);

    const result = await this.searchResultService.processHybridChunksResult(
      fullTextResult,
      semanticResult,
      {
        query: finalQuery,
        userId,
        enrichMetadata,
        maxResults,
        rerank,
      },
    );
    return result;
  }

  async hybridSearchEntities(params: HybridSearchChunksParams) {
    const result = await this.hybridSearchChunks(params);

    // Group results by entity_type and entity_id
    const groups = groupBy(result, (r) => `${r.type}_${r.id}`);
    // Create sources and chunks arrays maintaining the relationship
    const entity_and_chunks = Object.values(groups).map((group) => {
      let related_chunk = group.map((r) => r.chunk).join('\n');
      if (params.enrichMetadata) {
        related_chunk = `${group[0].title || ''}\n\n${related_chunk}`;
      }

      return {
        entity_id: group[0].id,
        entity_type: group[0].type as SearchResultTypeEnum,
        related_chunk,
      };
    });

    return entity_and_chunks;
  }

  async hybridSearch({
    query,
    userId,
    fields = DEFAULT_SEARCH_FIELDS,
    boardIds,
  }: {
    query: string;
    userId: string;
    fields?: string[];
    boardIds?: string[];
  }) {
    try {
      // Run both semantic and full-text searches in parallel
      const [semanticResults, fullTextResults] = await Promise.all([
        this.semanticSearchChunks({
          query,
          userId,
          maxResults: 10,
          threshold: DEFAULT_SEMANTIC_DISTANCE,
          boardIds,
          fields,
        }),
        this.search({
          query,
          userId,
          fields,
          boardIds,
        }),
      ]);

      // Fetch additional fields for semantic results
      const semanticSnipIds = semanticResults
        .filter((result) => result.type === 'snip')
        .map((result) => result.id);
      const additionalFields =
        semanticSnipIds.length > 0
          ? await this.typesense.fetchSnips(semanticSnipIds)
          : { hits: [] };

      // Create a map of additional fields by ID
      const snipFieldsMap = new Map<string, any>();
      if (additionalFields.hits) {
        for (const hit of additionalFields.hits) {
          const id = (hit.document as { id: string }).id;
          const fields = restoreLocalizedFields(hit.document as Partial<LocalizedObject>);
          snipFieldsMap.set(id, fields);
        }
      }

      // Convert semantic results to match Typesense format
      const semanticHits = semanticResults.map((result) => ({
        document: {
          ...(snipFieldsMap.get(result.id) || {}),
          id: result.id,
          docType: result.type || 'snip',
          content_0: result.chunk || '',
          updated_at: result.updated_at ? new Date(result.updated_at).valueOf() : Date.now(),
          original_field: result.original_field,
          chunk_index: result.chunk_index,
        } as Partial<SnipSearchDocument | ThoughtSearchDocument>,
        highlight: {
          [result.original_field || 'content']: {
            snippet: result.chunk || '',
          },
        },
        text_match: 1 - (result.distance || 0), // Convert distance to similarity score
      }));

      // Combine hits from both sources, prioritizing exact matches
      const combinedHits = [...(fullTextResults.hits || []), ...semanticHits].sort(
        (a, b) => (b.text_match || 0) - (a.text_match || 0),
      );

      // Remove duplicates based on document ID, keeping the higher-scoring version
      const seenIds = new Set<string>();
      const uniqueHits = combinedHits.filter((hit) => {
        const id = hit.document?.id;
        if (!id || seenIds.has(id)) {
          return false;
        }
        seenIds.add(id);
        return true;
      });

      return {
        hits: uniqueHits,
        found: uniqueHits.length,
      };
    } catch (error) {
      return {
        hits: [],
        found: 0,
      };
    }
  }

  private async performTypesenseSearch(
    searches: MultiSearchRequestSchema[],
    searchOptions?: Partial<MultiSearchRequestSchema>,
  ) {
    const finalOptions: Partial<MultiSearchRequestSchema> & Record<string, unknown> = {
      prefix: false,
      enable_highlight_v1: false,
      highlight_affix_num_tokens: 50,
      exclude_fields: 'embedding',
      text_match_type: 'max_weight',
      per_page: 50,
      ...searchOptions,
    };

    // 解决 include_fields, exclude_fields 等字段需要 localized 的问题
    for (const [key, value] of Object.entries(finalOptions)) {
      if (key.endsWith('_fields') && typeof value === 'string' && value.trim() !== '') {
        const fields = value.split(',').map((field) => field.trim());
        const localizedFields = fields.flatMap((field) => toLocalizedFieldNames(field));
        finalOptions[key] = localizedFields.join(',');
      }
    }

    this.logger.debug(
      `[search] performing typesense search with options: ${JSON.stringify(finalOptions)} and searches: ${JSON.stringify(searches)}`,
    );

    const result = await this.typesense.performMultiSearch<
      [SnipSearchDocument, ThoughtSearchDocument],
      false
    >(
      {
        searches,
      },
      finalOptions,
    );

    const processedResult = await this.searchResultService.processSearchResult(result);
    return processedResult;
  }

  async listCollections() {
    return this.typesense.listCollections();
  }

  async createCollection(name: TypesenseCollection, env = getServicesEnv() as YouMindEnv) {
    const schema = SCHEMA_MAP[name];
    if (!schema) {
      throw new Error(`Unsupported collection ${name}`);
    }

    return this.typesense.createCollection(
      {
        name,
        fields: schema,
      },
      env,
    );
  }

  async dropCollection(name: TypesenseCollection, env = getServicesEnv() as YouMindEnv) {
    return this.typesense.dropCollection(name, env);
  }

  async syncDocuments({
    entities,
    collection,
    env = getServicesEnv() as YouMindEnv,
    docType,
    embedding = true,
  }: {
    entities: EntityToBeIndexed[];
    collection: TypesenseCollection;
    docType: 'snip' | 'thought';
    env?: YouMindEnv;
    embedding?: boolean;
    userId?: string;
  }): Promise<void> {
    const documents = await Promise.all(
      entities.map((entity) => this.searchDocumentService.toSearchDocument({ entity, docType })),
    );
    const documentsToIndex = documents.flat();
    if (documentsToIndex.length === 0) {
      return;
    }

    const syncTask = this.typesense.bulkUpsertDocuments(collection, documentsToIndex, env);

    if (embedding) {
      this.logger.debug(`[search] syncing clickhouse embeddings for ${entities.length} entities`);
      const clickhouseEmbedTask = this.syncClickhouseEmbeddings({
        entities,
        docType,
      });
      await Promise.all([syncTask, clickhouseEmbedTask]);
      this.logger.debug(`[search] synced clickhouse embeddings for ${entities.length} entities`);
    } else {
      await syncTask;
    }
  }

  async syncClickhouseEmbeddings({
    entities,
    docType,
  }: {
    entities: EntityToBeIndexed[];
    docType: 'snip' | 'thought';
  }) {
    return Promise.all(
      entities.map((entity) =>
        this.searchDocumentService.toEmbeddingsTableItem({ entity, docType }),
      ),
    ).then((embedResults) => {
      return this.clickhouseService.bulkUpsert(
        ClickhouseTable.YOUMIND_ENTITY_EMBEDDINGS,
        embedResults.flat(),
      );
    });
  }

  async deleteDocuments(
    collection: TypesenseCollection,
    entityIds: string[],
    env = getServicesEnv() as YouMindEnv,
  ): Promise<void> {
    await Promise.all([
      this.typesense.deleteDocuments(collection, entityIds, env),
      this.clickhouseService.deleteEmbeddingsByEntityTypeAndIds(
        this.clickhouseService.toClickhouseEntityType(collection),
        entityIds,
      ),
    ]);
  }

  private prepared = false;
  async prepareEnv(env = getServicesEnv() as YouMindEnv) {
    if (this.prepared) {
      return;
    }
    await Promise.all(
      Object.values(TypesenseCollection).map((collection) =>
        this.typesense.retrieveCollection(collection, env).catch((err) => {
          const errorMessage = err.message;
          this.logger.warn(errorMessage, `[search] check search preparation failed:`);
          this.logger.log(`[search] recreating collection ${collection} for env: ${env}`);

          return this.createCollection(collection, env);
        }),
      ),
    );
    this.prepared = true;
  }
}

// Export other search services
export { InternetSearchDomainService } from './internet';
