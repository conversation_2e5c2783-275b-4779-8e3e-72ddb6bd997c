# Material-mng 模块测试计划

## 概述

本文档为 material-mng 模块制定全面的单元测试和集成测试计划。测试遵循 IAM 模块的测试模式和最佳实践。

## 单元测试计划

### 1. Domain 层测试

#### 1.1 实体（Entities）测试

**Snip 相关实体**
- `snip.entity.spec.ts` - 基础 Snip 实体测试
  - 创建和初始化测试
  - 状态变更测试
  - 位置信息管理测试
  - 可见性管理测试
  
- `article.entity.spec.ts` - Article 实体测试
  - 创建文章测试
  - 内容和元数据验证
  - 作者信息管理
  
- `image.entity.spec.ts` - Image 实体测试
  - 图片信息创建和更新
  - URL 处理和验证
  - 尺寸信息管理
  
- `video.entity.spec.ts` - Video 实体测试
  - 视频创建和状态管理
  - 转码状态跟踪
  - 播放 URL 管理
  
- `voice.entity.spec.ts` - Voice 实体测试
  - 音频文件管理
  - 转写状态跟踪
  - 时长信息验证

**Board 相关实体**
- `board.entity.spec.ts` - Board 实体测试
  - Board 创建和更新
  - 状态管理（激活、归档、固定）
  - 时间戳管理
  
- `board-group.entity.spec.ts` - BoardGroup 实体测试
  - 分组创建和管理
  - 层级关系验证
  - 排序逻辑测试
  
- `board-item.entity.spec.ts` - BoardItem 实体测试
  - 位置信息管理
  - 移动操作验证
  - 关联关系管理

**其他核心实体**
- `thought.entity.spec.ts` - Thought 实体测试
- `note.entity.spec.ts` - Note 实体测试
- `block.entity.spec.ts` - Block 实体测试
- `favorite.entity.spec.ts` - Favorite 实体测试

#### 1.2 值对象（Value Objects）测试

- `webpage-meta.vo.spec.ts` - 网页元数据值对象测试
- `content.vo.spec.ts` - 内容值对象测试
- `page-data.vo.spec.ts` - 页面数据值对象测试

#### 1.3 领域服务测试

- `board-position.service.spec.ts` - 板块位置服务测试
  - 位置计算逻辑
  - 移动策略验证
  - 冲突解决机制

### 2. Repository 层测试

- `snip.repository.spec.ts` - Snip 仓储测试
  - CRUD 操作测试
  - 批量查询测试
  - 过滤和分页测试
  - 事务处理测试
  
- `board.repository.spec.ts` - Board 仓储测试
  - Board 查询和创建
  - 默认 Board 获取
  - 批量操作测试
  
- `board-item.repository.spec.ts` - BoardItem 仓储测试
  - 位置信息持久化
  - 移动操作事务
  - 关联查询测试

### 3. Service 层测试

#### 3.1 Command Handlers 测试

**Snip 相关**
- `create-article.handler.spec.ts`
- `create-image.handler.spec.ts`
- `create-video.handler.spec.ts`
- `update-snip.handler.spec.ts`
- `delete-snip.handler.spec.ts`

**Board 相关**
- `create-board.handler.spec.ts`
- `update-board.handler.spec.ts`
- `archive-board.handler.spec.ts`
- `move-board-item.handler.spec.ts`

#### 3.2 Query Handlers 测试

- `get-snip.handler.spec.ts`
- `list-snips.handler.spec.ts`
- `get-board.handler.spec.ts`
- `list-boards.handler.spec.ts`

#### 3.3 DTO Services 测试

- `snip-dto.service.spec.ts` - Snip DTO 转换服务测试
- `board-dto.service.spec.ts` - Board DTO 转换服务测试

### 4. Decorators 测试

- `check-snip-quota.decorator.spec.ts` - Snip 配额检查装饰器测试

## 集成测试计划

### 1. API 端点测试

#### 1.1 Snip 相关 API

**Article API 测试** (`article.controller.e2e-spec.ts`)
- POST /api/v1/article/create - 创建文章
- PATCH /api/v1/article/:id - 更新文章
- DELETE /api/v1/article/:id - 删除文章
- GET /api/v1/article/:id - 获取文章详情

**Image API 测试** (`image.controller.e2e-spec.ts`)
- POST /api/v1/image/create - 创建图片
- POST /api/v1/image/edit - 编辑图片
- PATCH /api/v1/image/:id - 更新图片信息

**Video API 测试** (`video.controller.e2e-spec.ts`)
- POST /api/v1/video/create - 创建视频
- PATCH /api/v1/video/:id/transcript - 更新转写
- POST /api/v1/video/:id/generate-transcript - 生成转写

#### 1.2 Board 相关 API

**Board API 测试** (`board.controller.e2e-spec.ts`)
- POST /api/v1/board/create - 创建板块
- GET /api/v1/board/list - 获取板块列表
- PATCH /api/v1/board/:id - 更新板块
- POST /api/v1/board/:id/archive - 归档板块
- POST /api/v1/board/:id/pin - 固定板块

**Board Item 移动测试** (`board-item-move.e2e-spec.ts`)
- POST /api/v1/move/entity-to-board - 移动实体到板块
- POST /api/v1/move/board-item-to-group - 移动到分组
- POST /api/v1/move/board-item-to-root - 移动到根目录

#### 1.3 Favorite 相关 API

**Favorite API 测试** (`favorite.controller.e2e-spec.ts`)
- POST /api/v1/favorite/entity - 收藏实体
- DELETE /api/v1/favorite/:id - 取消收藏
- GET /api/v1/favorite/list - 获取收藏列表
- POST /api/v1/favorite/move - 移动收藏

### 2. 业务流程测试

#### 2.1 Snip 生命周期测试

- 创建 -> 更新 -> 发布 -> 删除 完整流程
- 不同类型 Snip 的特定流程测试
- 批量操作测试

#### 2.2 Board 组织结构测试

- Board 创建和管理流程
- BoardGroup 嵌套结构测试
- BoardItem 移动和重组测试

#### 2.3 权限和配额测试

- Space 权限验证测试
- Snip 创建配额限制测试
- 跨 Space 访问控制测试

### 3. 性能测试

- 大量 Snip 查询性能测试
- 批量移动操作性能测试
- 复杂过滤条件查询测试

## 测试工具和配置

### 测试框架
- Jest - 单元测试和集成测试框架
- Supertest - HTTP 测试
- @nestjs/testing - NestJS 测试工具

### Mock 和 Stub
- 数据库事务 Mock
- 外部服务 Mock (Youget, AI services)
- 事件总线 Mock

### 测试数据准备
- 使用 Factory 模式创建测试数据
- 测试数据清理策略
- 固定测试数据集

## 测试覆盖率目标

- 单元测试覆盖率：>= 80%
- 集成测试覆盖率：>= 70%
- 关键业务流程：100%

## 测试执行策略

### 本地开发
```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:e2e

# 运行特定模块测试
npm test -- material-mng
```

### CI/CD 集成
- PR 提交时运行所有测试
- 主分支合并时运行完整测试套件
- 每日运行性能测试

## 测试维护

- 定期审查失败的测试
- 更新测试以匹配新功能
- 保持测试代码质量
- 文档化复杂的测试场景