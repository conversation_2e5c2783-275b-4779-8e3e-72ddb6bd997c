/**
 * Overview Entity - 概览区块实体
 * Overview 块是 Block 的子类，用于存储内容的概览/总结
 * 特点：最多只有一个 content
 *
 * Migrated from:
 * - /youapp/src/lib/domain/block/types.ts
 */

import { ContentFormatEnum } from '@repo/common';
import { BlockDisplayEnum, SnipFeatureEnum } from '../../../../../common/types/snip.types';
import { Block, CreateBlockParams } from './block.entity';
import { BlockContent } from './block-content.entity';

export interface CreateOverviewParams extends Omit<CreateBlockParams, 'type'> {
  // Overview 特有的参数可以在这里添加
}

export class Overview extends Block {
  constructor(
    id: string,
    createdAt: Date,
    updatedAt: Date,
    snipId: string,
    display: BlockDisplayEnum,
    currentContentId?: string,
  ) {
    // Overview 的 type 固定为 OVERVIEW
    super(id, createdAt, updatedAt, snipId, SnipFeatureEnum.OVERVIEW, display, currentContentId);
  }

  static create(params: CreateOverviewParams): Overview {
    // 使用父类的 create 方法创建基础 block
    const baseBlock = Block.create({
      ...params,
      type: SnipFeatureEnum.OVERVIEW,
    });

    // 转换为 Overview 实例
    const overview = new Overview(
      baseBlock.id,
      baseBlock.createdAt,
      baseBlock.updatedAt,
      baseBlock.snipId,
      baseBlock.display,
      baseBlock.currentContentId,
    );

    overview.isNew = true;

    // 继承已应用的事件，不太理解 AI 生成这个什么意思，先注释掉
    // overview.uncommittedEvents = baseBlock.getUncommittedEvents();

    return overview;
  }

  /**
   * 添加内容 - 重写父类方法以强制只能有一个内容
   */
  addContent(content: BlockContent): void {
    // 如果已经有内容，抛出错误
    if (this.contents.length > 0) {
      throw new Error('Overview block can only have one content');
    }

    // 验证内容格式应该是 LLM_OUTPUT
    if (content.format !== ContentFormatEnum.LLM_OUTPUT) {
      throw new Error(`Overview content must be of format LLM_OUTPUT, got ${content.format}`);
    }

    super.addContent(content);
  }

  /**
   * 替换内容 - Overview 特有方法，用于更新唯一的内容
   */
  replaceContent(newContent: BlockContent): void {
    // 验证内容格式
    if (newContent.format !== ContentFormatEnum.LLM_OUTPUT) {
      throw new Error(`Overview content must be of format LLM_OUTPUT, got ${newContent.format}`);
    }

    // 清空现有内容
    this.contents = [];

    // 添加新内容
    this.addContent(newContent);
  }

  /**
   * 获取概览内容 - 简化的访问方法
   */
  getOverviewContent(): BlockContent | undefined {
    return this.contents[0];
  }

  /**
   * 检查是否有概览内容
   */
  hasContent(): boolean {
    return this.contents.length > 0;
  }
}
