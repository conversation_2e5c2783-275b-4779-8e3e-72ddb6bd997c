/**
 * Transcript Entity - 转录区块实体
 * Transcript 块是 Block 的子类，用于存储音视频内容的转录文本
 * 特点：可以有多个 content，但每个 format 类型最多只有一个
 *
 * Migrated from:
 * - /youapp/src/lib/domain/block/types.ts
 */

import { ContentFormatEnum } from '@repo/common';
import { BlockDisplayEnum, SnipFeatureEnum } from '../../../../../common/types/snip.types';
import { Block, CreateBlockParams } from './block.entity';
import { BlockContent } from './block-content.entity';

export interface CreateTranscriptParams extends Omit<CreateBlockParams, 'type'> {
  // Transcript 特有的参数可以在这里添加
}

export class Transcript extends Block {
  // 用于快速查找特定格式的内容
  private contentsByFormat: Map<ContentFormatEnum | string, BlockContent> = new Map();

  constructor(
    id: string,
    createdAt: Date,
    updatedAt: Date,
    snipId: string,
    display: BlockDisplayEnum,
    currentContentId?: string,
  ) {
    // Transcript 的 type 固定为 TRANSCRIPT
    super(id, createdAt, updatedAt, snipId, SnipFeatureEnum.TRANSCRIPT, display, currentContentId);
  }

  static create(params: CreateTranscriptParams): Transcript {
    // 使用父类的 create 方法创建基础 block
    const baseBlock = Block.create({
      ...params,
      type: SnipFeatureEnum.TRANSCRIPT,
    });

    // 转换为 Transcript 实例
    const transcript = new Transcript(
      baseBlock.id,
      baseBlock.createdAt,
      baseBlock.updatedAt,
      baseBlock.snipId,
      baseBlock.display,
      baseBlock.currentContentId,
    );

    transcript.isNew = true;

    // 继承已应用的事件，不太理解 AI 生成这个什么意思，先注释掉
    // transcript.uncommittedEvents = baseBlock.getUncommittedEvents();

    return transcript;
  }

  /**
   * 添加内容 - 重写父类方法以确保每个 format 类型只有一个内容
   */
  addContent(content: BlockContent): void {
    // 检查是否已存在相同格式的内容
    if (this.contentsByFormat.has(content.format)) {
      throw new Error(`Transcript already has content with format ${content.format}`);
    }

    // 验证内容格式是否为已知的转录格式
    const validFormats = [ContentFormatEnum.SUBTITLE, ContentFormatEnum.SUBTITLE_FORMATTED];

    if (!validFormats.includes(content.format)) {
      // 记录警告但不阻止添加，以支持未来可能的新格式
      console.warn(`Adding content with unknown transcript format: ${content.format}`);
    }

    // 添加到 map 中以便快速查找
    this.contentsByFormat.set(content.format, content);

    // 调用父类方法
    super.addContent(content);
  }

  /**
   * 移除内容 - 重写父类方法以同步更新 format map
   */
  removeContent(contentId: string): void {
    // 找到要移除的内容
    const content = this.contents.find((c) => c.id === contentId);
    if (content) {
      this.contentsByFormat.delete(content.format);
    }

    super.removeContent(contentId);
  }

  /**
   * 根据格式获取内容
   */
  getContentByFormat(format: ContentFormatEnum | string): BlockContent | undefined {
    return this.contentsByFormat.get(format);
  }

  /**
   * 获取原始字幕内容
   */
  getSubtitleContent(): BlockContent | undefined {
    return this.getContentByFormat(ContentFormatEnum.SUBTITLE);
  }

  /**
   * 获取格式化后的字幕内容
   */
  getFormattedSubtitleContent(): BlockContent | undefined {
    return this.getContentByFormat(ContentFormatEnum.SUBTITLE_FORMATTED);
  }

  /**
   * 检查是否有特定格式的内容
   */
  hasContentFormat(format: ContentFormatEnum | string): boolean {
    return this.contentsByFormat.has(format);
  }

  /**
   * 替换特定格式的内容
   */
  replaceContentByFormat(format: ContentFormatEnum | string, newContent: BlockContent): void {
    if (newContent.format !== format) {
      throw new Error(`Content format mismatch: expected ${format}, got ${newContent.format}`);
    }

    // 移除旧内容
    const oldContent = this.contentsByFormat.get(format);
    if (oldContent) {
      this.removeContent(oldContent.id);
    }

    // 添加新内容
    this.addContent(newContent);
  }

  /**
   * 获取所有支持的格式列表
   */
  getSupportedFormats(): string[] {
    return Array.from(this.contentsByFormat.keys());
  }

  /**
   * 初始化内容格式映射（用于从数据库加载时）
   */
  initializeContentMap(): void {
    this.contentsByFormat.clear();
    for (const content of this.contents) {
      this.contentsByFormat.set(content.format, content);
    }
  }
}
