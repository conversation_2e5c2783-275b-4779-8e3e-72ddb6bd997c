import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Board } from '../../domain/board/models/board.entity';
import { BoardItem, BoardItemType } from '../../domain/board-item/models/board-item.entity';
import { Snip } from '../../domain/snip/models/snip.entity';
import { Thought } from '../../domain/thought/models/thought.entity';
import {
  BoardDto,
  BoardItemDto,
  BoardWithCountDto,
  BoardWithSomeBoardItemsDto,
} from '../../dto/board.dto';
import { BoardRepository } from '../../repositories/board.repository';
import { BoardItemRepository } from '../../repositories/board-item.repository';
import { SnipRepository } from '../../repositories/snip.repository';
import { ThoughtRepository } from '../../repositories/thought.repository';
import { SnipDtoService } from './snip-dto.service';
import { ThoughtDtoService } from './thought-dto.service';

@Injectable()
export class BoardDtoService {
  constructor(
    private readonly boardRepository: BoardRepository,
    private readonly boardItemRepository: BoardItemRepository,
    private readonly snipRepository: SnipRepository,
    private readonly thoughtRepository: ThoughtRepository,
    @Inject(forwardRef(() => SnipDtoService))
    private readonly snipDtoService: SnipDtoService,
    private readonly thoughtDtoService: ThoughtDtoService,
  ) {}
  toDto(board: Board): BoardDto {
    if (board == null) {
      return null;
    }

    return Object.assign(new BoardDto(), {
      id: board.id,
      spaceId: board.spaceId,
      createdAt: board.createdAt,
      updatedAt: board.updatedAt,
      name: board.name,
      description: board.description,
      icon: {
        name: board.icon?.name,
        color: board.icon?.color,
      },
      pinnedAt: board.pinnedAt,
      status: board.status,
      heroImageUrls: board.heroImageUrls,
      intro: board.intro,
      type: board.type,
    });
  }

  toBoardWithCountDto(
    board: Board,
    snipsCount: number = 0,
    thoughtsCount: number = 0,
  ): BoardWithCountDto {
    return Object.assign(new BoardWithCountDto(), {
      ...this.toDto(board),
      snipsCount,
      thoughtsCount,
    });
  }

  toDtos(boards: Board[]): BoardDto[] {
    return boards.map((board) => this.toDto(board));
  }

  async toBoardWithCountDtoList(boards: Board[]): Promise<BoardWithCountDto[]> {
    if (boards.length === 0) {
      return [];
    }

    // 批量获取计数
    const boardIds = boards.map((board) => board.id);
    const countsMap = await this.boardRepository.countItemsByBoardIds(boardIds);

    // 转换为 DTO
    return boards.map((board) => {
      const counts = countsMap[board.id] || { snipsCount: 0, thoughtsCount: 0 };
      return this.toBoardWithCountDto(board, counts.snipsCount, counts.thoughtsCount);
    });
  }

  /**
   * 优化版本：批量获取看板及其项目，参考 youapp 的 boards2withSomeBoardItemsVOs 逻辑
   */
  async toBoardWithSomeBoardItemsDtoList(
    boards: Board[],
    limit: number = 10,
    spaceId?: string,
  ): Promise<BoardWithSomeBoardItemsDto[]> {
    if (boards.length === 0) {
      return [];
    }

    // 步骤1: 并行获取每个看板的数据。FIXME: 此处存在 itemsCount 虚高的问题，是由于 boardItem 表未删除，但 snip 和 thought 表删除的情况。可通过定时任务来确保一致性
    const boardsWithData = await Promise.all(
      boards.map(async (board) => {
        const [{ data: items }, itemsCount] = await Promise.all([
          this.boardItemRepository.findByCursorExcludeGroup(board.id, limit),
          this.boardItemRepository.countExcludeGroup(board.id),
        ]);
        return { board, items, itemsCount };
      }),
    );

    // 步骤2: 收集所有需要查询的实体ID
    const allSnipIds = new Set<string>();
    const allThoughtIds = new Set<string>();
    const allChatIds = new Set<string>();

    boardsWithData.forEach(({ items }) => {
      items.forEach((item) => {
        if (item.entityType === BoardItemType.SNIP && item.entityId) {
          allSnipIds.add(item.entityId);
        } else if (item.entityType === BoardItemType.THOUGHT && item.entityId) {
          allThoughtIds.add(item.entityId);
        } else if (item.entityType === BoardItemType.CHAT && item.entityId) {
          allChatIds.add(item.entityId);
        }
      });
    });

    // 步骤3: 批量查询所有实体（核心优化点）
    const [snips, thoughts] = await Promise.all([
      allSnipIds.size > 0
        ? this.snipRepository.findByIdsWithContentTruncated({
            ids: Array.from(allSnipIds),
            includeDeleted: false,
          })
        : [],
      allThoughtIds.size > 0
        ? this.thoughtRepository.findByIds({ ids: Array.from(allThoughtIds), spaceId })
        : [],
    ]);

    // 步骤4: 创建高效的查找映射
    const snipMap = new Map<string, Snip>();
    snips.forEach((snip) => snipMap.set(snip.id, snip));
    const thoughtMap = new Map<string, Thought>();
    thoughts.forEach((thought) => thoughtMap.set(thought.id, thought));

    // 步骤5: 组装最终结果
    return boardsWithData.map(({ board, items, itemsCount }) =>
      Object.assign(new BoardWithSomeBoardItemsDto(), {
        ...this.toDto(board),
        itemsCount,
        items: items
          .map((item) => this.createBoardItemDto(item, snipMap, thoughtMap))
          .filter((item) => item != null && item.entity != null), // 存在 boardItem 未删除，但 snip 或 thought 已删除的情况
      }),
    );
  }

  /**
   * 从映射中创建 BoardItemDto，避免单独查询
   */
  private createBoardItemDto(
    item: BoardItem,
    snipMap: Map<string, Snip>,
    thoughtMap: Map<string, Thought>,
  ): BoardItemDto {
    let entity = null;

    if (item.entityType === BoardItemType.SNIP && item.entityId) {
      const snip = snipMap.get(item.entityId);
      if (snip) {
        entity = this.snipDtoService.toDto(snip);
      }
    } else if (item.entityType === BoardItemType.THOUGHT && item.entityId) {
      const thought = thoughtMap.get(item.entityId);
      if (thought) {
        entity = this.thoughtDtoService.toDto(thought);
      }
    }

    return Object.assign(new BoardItemDto(), {
      id: item.id,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      boardId: item.boardId,
      parentBoardGroupId: item.parentBoardGroupId,
      rank: item.rank,
      snipId: item.entityType === BoardItemType.SNIP ? item.entityId : null,
      thoughtId: item.entityType === BoardItemType.THOUGHT ? item.entityId : null,
      boardGroupId: item.entityType === BoardItemType.BOARD_GROUP ? item.entityId : null,
      chatId: item.entityType === BoardItemType.CHAT ? item.entityId : null,
      entityType: item.entityType,
      entity: entity,
    });
  }
}
