/**
 * Material Management CQRS Handler - Upsert Snip Blocks
 * 处理更新/创建 Snip 内容块的命令（PUT 语义）
 *
 * Migrated from:
 * - youapp/lib/domain/snip/index.ts (createSnipBlocks method)
 */

import { BadRequestException, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import {
  ContentFormatEnum,
  createContentHandler,
  LanguageEnum,
  ProcessStatusEnum,
  SubtitleHandler,
} from '@repo/common';
import { BlockDisplayEnum, SnipFeatureEnum } from '@/common/types';
import { BlockContent } from '@/modules/material-mng/domain/block/models/block-content.entity';
import { Overview } from '@/modules/material-mng/domain/block/models/overview.entity';
import { Transcript } from '@/modules/material-mng/domain/block/models/transcript.entity';
import { BlockRepository } from '@/modules/material-mng/repositories/block.repository';
import { BlockContentRepository } from '@/modules/material-mng/repositories/block-content.repository';
import { SnipRepository } from '@/modules/material-mng/repositories/snip.repository';
import {
  CreateBlockContentParam,
  UpsertSnipBlocksCommand,
} from '../../commands/snip/upsert-snip-blocks.command';

/**
 * 本质是 PUT，保证唯一性，如果存在则更新，不存在则创建
 */
@CommandHandler(UpsertSnipBlocksCommand)
export class UpsertSnipBlocksHandler implements ICommandHandler<UpsertSnipBlocksCommand> {
  private readonly logger = new Logger(UpsertSnipBlocksHandler.name);

  constructor(
    private readonly blockRepository: BlockRepository,
    private readonly contentRepository: BlockContentRepository,
    private readonly snipRepository: SnipRepository,
  ) {}

  async execute(command: UpsertSnipBlocksCommand): Promise<void> {
    const { snipId, params } = command;

    this.logger.log(`Creating snip blocks for snip: ${snipId}`);

    // 验证 snip 是否存在
    const snip = await this.snipRepository.findById(snipId);
    if (!snip) {
      throw new BadRequestException(`Snip with id ${snipId} not found`);
    }

    try {
      // 创建 transcript 内容块
      if (params.transcriptContents && params.transcriptContents.length > 0) {
        await this.createTranscriptBlocks(snipId, params.transcriptContents);
      }

      // 创建 overview 内容块
      if (params.overviewContents && params.overviewContents.length > 0) {
        await this.createOverviewBlocks(snipId, params.overviewContents);
      }

      this.logger.log(`Successfully created snip blocks for snip: ${snipId}`);
    } catch (error) {
      this.logger.error(error, `Failed to create snip blocks for snip: ${snipId}`);
      throw error;
    }
  }

  /**
   * 创建转录内容块
   */
  private async createTranscriptBlocks(
    snipId: string,
    transcriptContents: CreateBlockContentParam[],
  ): Promise<void> {
    // 查找现有的 transcript block（一个 snip 只有一个 transcript block）
    const existingBlocks = await this.blockRepository.findBySnipId(snipId);
    let transcript = existingBlocks.find(
      (b) => b.type === SnipFeatureEnum.TRANSCRIPT,
    ) as Transcript;

    if (!transcript) {
      // 创建新的 Transcript 实体
      transcript = Transcript.create({
        snipId,
        display: BlockDisplayEnum.SHOW,
      });
      await this.blockRepository.save(transcript);
    } else {
      // 加载现有的 contents
      const existingContents = await this.blockRepository.findContentsByBlockId(transcript.id);
      transcript.contents = existingContents;
      transcript.initializeContentMap();
    }

    // 按 format 分组，每个 format 类型最多只有一个 content
    const contentsByFormat = new Map<ContentFormatEnum, CreateBlockContentParam>();

    for (const content of transcriptContents) {
      const format = content.format ?? ContentFormatEnum.SUBTITLE;

      // 如果已存在相同 format，跳过后续的（保留第一个）
      if (!contentsByFormat.has(format)) {
        contentsByFormat.set(format, content);
      } else {
        this.logger.warn(`Duplicate transcript content format ${format} found, skipping`);
      }
    }

    // 为每个唯一的 format 创建或更新内容
    let subtitleContent: BlockContent | undefined;

    for (const [format, contentParam] of contentsByFormat) {
      const existingContent = transcript.getContentByFormat(format);

      if (existingContent) {
        // 更新现有内容
        const updatedContent = await this.updateExistingContent(existingContent, contentParam);
        if (format === ContentFormatEnum.SUBTITLE) {
          subtitleContent = updatedContent;
        }
      } else {
        // 创建新内容
        const newContent = await this.createAndSaveContent(transcript.id, contentParam, format);
        if (newContent) {
          transcript.addContent(newContent);
          if (format === ContentFormatEnum.SUBTITLE) {
            subtitleContent = newContent;
          }
        }
      }
    }

    // 设置 currentContentId 指向 subtitle content
    if (subtitleContent) {
      transcript.setCurrentContent(subtitleContent.id);
      await this.blockRepository.save(transcript);
    }
  }

  /**
   * 创建概览内容块 - 最多只有一个 content
   */
  private async createOverviewBlocks(
    snipId: string,
    overviewContents: CreateBlockContentParam[],
  ): Promise<void> {
    // Overview 最多只有一个 content，取第一个
    if (overviewContents.length > 1) {
      this.logger.warn(
        `Overview block should have at most 1 content, but got ${overviewContents.length}. Using the first one.`,
      );
    }

    const firstContent = overviewContents[0];
    if (!firstContent) {
      this.logger.warn('No overview content provided');
      return;
    }

    // 查找现有的 overview block（一个 snip 只有一个 overview block）
    const existingBlocks = await this.blockRepository.findBySnipId(snipId);
    let overview = existingBlocks.find((b) => b.type === SnipFeatureEnum.OVERVIEW) as Overview;

    if (!overview) {
      // 创建新的 Overview 实体
      overview = Overview.create({
        snipId,
        display: BlockDisplayEnum.SHOW,
      });
      await this.blockRepository.save(overview);
    }

    // 查找现有的 content（overview 只有一个 content）
    const existingContents = await this.blockRepository.findContentsByBlockId(overview.id);

    if (existingContents.length > 0) {
      // 更新现有内容
      const existingContent = existingContents[0];
      await this.updateExistingContent(existingContent, firstContent);

      // 确保 currentContentId 指向这个 content
      if (overview.currentContentId !== existingContent.id) {
        overview.setCurrentContent(existingContent.id);
        await this.blockRepository.save(overview);
      }
    } else {
      // 创建新内容
      const content = await this.createAndSaveContent(
        overview.id,
        firstContent,
        ContentFormatEnum.LLM_OUTPUT,
      );

      if (content) {
        overview.addContent(content);
        overview.setCurrentContent(content.id);
        await this.blockRepository.save(overview);
      }
    }
  }

  /**
   * 创建并保存单个内容实体
   */
  private async createAndSaveContent(
    blockId: string,
    contentParam: CreateBlockContentParam,
    defaultFormat: ContentFormatEnum,
  ): Promise<BlockContent | null> {
    if (!contentParam) {
      return null;
    }

    const format = contentParam.format ?? defaultFormat;
    const language = contentParam.language ?? LanguageEnum['zh-CN'];

    let processedContent: any;

    // 对于 SUBTITLE 格式，需要特殊处理
    if (format === ContentFormatEnum.SUBTITLE) {
      // 尝试解析 raw 为 WhisperSubtitle 结构
      try {
        const whisperData = JSON.parse(contentParam.raw);
        if (whisperData && whisperData.segments) {
          // 如果是 WhisperSubtitle 结构，使用 SubtitleHandler 处理
          const handler = SubtitleHandler.fromWhisperSubtitle(whisperData);
          processedContent = handler.toVO();
        } else {
          // 如果不是 segments 结构，直接使用 createContentHandler
          processedContent = createContentHandler({
            format,
            raw: contentParam.raw,
            plain: contentParam.plain,
          }).toVO();
        }
      } catch (error) {
        // JSON 解析失败，可能是纯文本格式，使用 createContentHandler
        processedContent = createContentHandler({
          format,
          raw: contentParam.raw,
          plain: contentParam.plain,
        }).toVO();
      }
    } else {
      // 非 SUBTITLE 格式，直接使用 createContentHandler
      processedContent = createContentHandler({
        format,
        raw: contentParam.raw,
        plain: contentParam.plain,
      }).toVO();
    }

    // 创建 BlockContent 实体
    const content = BlockContent.create({
      blockId,
      language,
      format,
      raw: processedContent.raw,
      plain: processedContent.plain,
      status: ProcessStatusEnum.DONE,
    });

    // 保存内容到数据库
    await this.contentRepository.save(content);

    return content;
  }

  /**
   * 更新现有内容
   */
  private async updateExistingContent(
    existingContent: BlockContent,
    contentParam: CreateBlockContentParam,
  ): Promise<BlockContent> {
    const format = contentParam.format ?? existingContent.format;
    const language = contentParam.language ?? existingContent.language;

    let processedContent: any;

    // 对于 SUBTITLE 格式，需要特殊处理
    if (format === ContentFormatEnum.SUBTITLE) {
      // 尝试解析 raw 为 WhisperSubtitle 结构
      try {
        const whisperData = JSON.parse(contentParam.raw);
        if (whisperData && whisperData.segments) {
          // 如果是 WhisperSubtitle 结构，使用 SubtitleHandler 处理
          const handler = SubtitleHandler.fromWhisperSubtitle(whisperData);
          processedContent = handler.toVO();
        } else {
          // 如果不是 segments 结构，直接使用 createContentHandler
          processedContent = createContentHandler({
            format,
            raw: contentParam.raw,
            plain: contentParam.plain,
          }).toVO();
        }
      } catch (error) {
        // JSON 解析失败，可能是纯文本格式，使用 createContentHandler
        processedContent = createContentHandler({
          format,
          raw: contentParam.raw,
          plain: contentParam.plain,
        }).toVO();
      }
    } else {
      // 非 SUBTITLE 格式，直接使用 createContentHandler
      processedContent = createContentHandler({
        format,
        raw: contentParam.raw,
        plain: contentParam.plain,
      }).toVO();
    }

    // 更新内容
    existingContent.update({
      raw: processedContent.raw,
      plain: processedContent.plain,
      status: ProcessStatusEnum.DONE,
    });

    // 保存更新
    await this.contentRepository.save(existingContent);

    return existingContent;
  }
}
