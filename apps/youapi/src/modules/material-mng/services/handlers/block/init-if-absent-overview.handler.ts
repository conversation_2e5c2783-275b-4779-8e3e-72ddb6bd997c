/**
 * InitIfAbsentOverviewHandler - 初始化概览内容处理器（仅在不存在时）
 * 负责获取现有的 overview content，如果不存在则创建
 */

import { CommandHandler, EventBus, ICommandHandler } from '@nestjs/cqrs';
import { ContentFormatEnum, ProcessStatusEnum } from '@repo/common';
import { BlockDisplayEnum } from '@/common/types/snip.types';
import { BlockContent } from '@/modules/material-mng/domain/block/models/block-content.entity';
import { Overview } from '@/modules/material-mng/domain/block/models/overview.entity';
import { BlockRepository } from '../../../repositories/block.repository';
import { InitIfAbsentOverviewCommand } from '../../commands/block/init-if-absent-overview.command';

export interface InitIfAbsentOverviewResult {
  overviewContent: BlockContent;
  isInitialized: boolean;
}

/**
 * 若 OverviewContent 不存在，则创建。提供给内部服务用的，先走 entity
 */
@CommandHandler(InitIfAbsentOverviewCommand)
export class InitIfAbsentOverviewHandler
  implements ICommandHandler<InitIfAbsentOverviewCommand, InitIfAbsentOverviewResult>
{
  constructor(
    private readonly blockRepository: BlockRepository,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: InitIfAbsentOverviewCommand): Promise<InitIfAbsentOverviewResult> {
    const { blockId, snipId, language } = command;

    let overview: Overview;

    if (blockId) {
      // 1. 获取现有的 overview block
      const existingBlock = await this.blockRepository.findById(blockId);
      if (!existingBlock) {
        throw new Error(`Overview block with id ${blockId} not found`);
      }
      // 将 Block 转换为 Overview（假设已经是 Overview 类型）
      overview = existingBlock as Overview;
    } else {
      // 1. 创建新的 overview
      overview = Overview.create({
        snipId,
        display: BlockDisplayEnum.SHOW,
        currentContentId: undefined,
      });
      await this.blockRepository.save(overview);
    }

    // 2. 查询 overview 下的 content，通常只有一个
    const existingContents = await this.blockRepository.findContentsByBlockId(overview.id);

    if (existingContents.length > 0) {
      // 存在 content，直接返回第一个
      return {
        overviewContent: existingContents[0],
        isInitialized: false,
      };
    }

    // 3. 不存在 content，创建新的初始 content
    const overviewContent = BlockContent.create({
      blockId: overview.id,
      snipId: snipId,
      language: language,
      format: ContentFormatEnum.LLM_OUTPUT,
      raw: '', // 初始为空
      plain: '', // 初始为空
      status: ProcessStatusEnum.ING, // 进行中
      traceId: undefined,
    });

    await this.blockRepository.saveContent(overviewContent);

    // 4. 更新 overview 的 currentContentId
    overview.currentContentId = overviewContent.id;
    await this.blockRepository.save(overview);

    // 5. 发布领域事件
    // overview.getUncommittedEvents().forEach((event) => {
    //   this.eventBus.publish(event);
    // });
    // overview.markEventsAsCommitted();

    return {
      overviewContent: overviewContent,
      isInitialized: true,
    };
  }
}
