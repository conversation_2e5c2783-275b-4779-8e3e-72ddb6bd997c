/**
 * InitIfAbsentTranscriptHandler - 初始化转录内容处理器（仅在不存在时）
 * 负责获取现有的 transcript content，如果不存在则创建
 * 与 Overview 不同，Transcript 可能有多个不同语言的 content
 */

import { CommandHandler, EventBus, ICommandHandler } from '@nestjs/cqrs';
import { ContentFormatEnum, ProcessStatusEnum } from '@repo/common';
import { BlockDisplayEnum } from '@/common/types/snip.types';
import { BlockContent } from '@/modules/material-mng/domain/block/models/block-content.entity';
import { Transcript } from '@/modules/material-mng/domain/block/models/transcript.entity';
import { BlockRepository } from '../../../repositories/block.repository';
import { InitIfAbsentTranscriptCommand } from '../../commands/block/init-if-absent-transcript.command';

export interface InitIfAbsentTranscriptResult {
  transcriptContent: BlockContent;
  isInitialized: boolean;
}

/**
 * 若对应 language 的 TranscriptContent 不存在，则创建。提供给内部服务用的，先走 entity
 */
@CommandHandler(InitIfAbsentTranscriptCommand)
export class InitIfAbsentTranscriptHandler
  implements ICommandHandler<InitIfAbsentTranscriptCommand, InitIfAbsentTranscriptResult>
{
  constructor(
    private readonly blockRepository: BlockRepository,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: InitIfAbsentTranscriptCommand): Promise<InitIfAbsentTranscriptResult> {
    const { blockId, snipId, language } = command;

    let transcript: Transcript;

    if (blockId) {
      // 1. 获取现有的 transcript block
      const existingBlock = await this.blockRepository.findById(blockId);
      if (!existingBlock) {
        throw new Error(`Transcript block with id ${blockId} not found`);
      }
      // 将 Block 转换为 Transcript（假设已经是 Transcript 类型）
      transcript = existingBlock as Transcript;
    } else {
      // 1. 创建新的 transcript
      transcript = Transcript.create({
        snipId,
        display: BlockDisplayEnum.SHOW,
        currentContentId: undefined,
      });
      await this.blockRepository.save(transcript);
    }

    // 2. 查询 transcript 下的 content
    const existingContents = await this.blockRepository.findContentsByBlockId(transcript.id);

    // 3. 根据 language 查找匹配的 content
    const matchingContent = existingContents.find(
      (content) => content.format === ContentFormatEnum.SUBTITLE,
    );

    if (matchingContent) {
      // 存在匹配语言的 content，直接返回
      return {
        transcriptContent: matchingContent,
        isInitialized: false,
      };
    }

    // 4. 不存在匹配语言的 content，创建新的初始 content
    const transcriptContent = BlockContent.create({
      blockId: transcript.id,
      snipId: snipId,
      language: language,
      format: ContentFormatEnum.SUBTITLE, // transcript 默认使用 SUBTITLE 格式
      raw: '', // 初始为空
      plain: '', // 初始为空
      status: ProcessStatusEnum.ING, // 进行中
      traceId: undefined,
    });

    await this.blockRepository.saveContent(transcriptContent);

    // 5. 如果是第一个 content，更新 transcript 的 currentContentId
    if (existingContents.length === 0) {
      transcript.currentContentId = transcriptContent.id;
      await this.blockRepository.save(transcript);
    }

    // 6. 发布领域事件
    // transcript.getUncommittedEvents().forEach((event) => {
    //   this.eventBus.publish(event);
    // });
    // transcript.markEventsAsCommitted();

    return {
      transcriptContent: transcriptContent,
      isInitialized: true,
    };
  }
}
