# 测试事务回滚策略

## 方案一：使用 Jest 的 beforeEach/afterEach 钩子管理事务

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { DrizzleService } from '@/common/database/drizzle.service';

describe('SnipRepository', () => {
  let module: TestingModule;
  let drizzleService: DrizzleService;
  let repository: SnipRepository;
  let trx: any; // 事务对象

  beforeAll(async () => {
    module = await Test.createTestingModule({
      providers: [SnipRepository, DrizzleService],
    }).compile();

    drizzleService = module.get<DrizzleService>(DrizzleService);
    repository = module.get<SnipRepository>(SnipRepository);
  });

  beforeEach(async () => {
    // 开始事务
    trx = await drizzleService.db.transaction();
    
    // 注入事务到 repository
    jest.spyOn(repository, 'db', 'get').mockReturnValue(trx);
  });

  afterEach(async () => {
    // 回滚事务，不提交任何更改
    await trx.rollback();
  });

  it('should create a snip without persisting to database', async () => {
    const snip = await Snip.create({
      spaceId: 'test-space',
      creatorId: 'test-user',
      title: 'Test Snip',
    });

    await repository.save(snip, trx);

    // 测试可以正常查询到数据
    const found = await repository.getById(snip.id, trx);
    expect(found).toBeDefined();
    expect(found.title).toBe('Test Snip');

    // 事务结束后，数据不会真正保存到数据库
  });
});
```

## 方案二：使用装饰器模式包装测试方法

```typescript
function TransactionalTest(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value;

  descriptor.value = async function (...args: any[]) {
    const drizzleService = this.drizzleService;
    
    return await drizzleService.db.transaction(async (trx) => {
      // 将事务注入到测试上下文
      this.trx = trx;
      
      try {
        // 执行测试
        await originalMethod.apply(this, args);
        
        // 测试完成后抛出错误以触发回滚
        throw new Error('ROLLBACK_TEST_TRANSACTION');
      } catch (error) {
        if (error.message === 'ROLLBACK_TEST_TRANSACTION') {
          // 这是预期的回滚，测试通过
          return;
        }
        // 真正的测试失败
        throw error;
      }
    });
  };

  return descriptor;
}

// 使用示例
class SnipRepositoryTest {
  @TransactionalTest
  async testCreateSnip() {
    const snip = await this.repository.save(snipData, this.trx);
    expect(snip).toBeDefined();
  }
}
```

## 方案三：使用测试基类封装事务管理

```typescript
abstract class TransactionalTestCase {
  protected trx: any;
  protected drizzleService: DrizzleService;

  async runInTransaction(testFn: () => Promise<void>): Promise<void> {
    try {
      await this.drizzleService.db.transaction(async (trx) => {
        this.trx = trx;
        await testFn();
        // 强制回滚
        throw new Error('FORCE_ROLLBACK');
      });
    } catch (error) {
      if (error.message !== 'FORCE_ROLLBACK') {
        throw error;
      }
    }
  }

  // Helper method to inject transaction into repositories
  protected injectTransaction(repository: BaseRepository): void {
    jest.spyOn(repository, 'db', 'get').mockReturnValue(this.trx);
  }
}

// 使用示例
class SnipServiceTest extends TransactionalTestCase {
  async testComplexBusinessLogic() {
    await this.runInTransaction(async () => {
      this.injectTransaction(this.snipRepository);
      this.injectTransaction(this.boardRepository);
      
      // 执行复杂的业务逻辑测试
      const snip = await this.snipService.createArticle(data);
      const board = await this.boardService.moveToBoard(snip.id, boardId);
      
      // 所有更改都会被回滚
    });
  }
}
```

## 方案四：使用 NestJS 测试模块的事务管理

```typescript
import { TransactionalTestModule } from './transactional-test.module';

describe('Material Management E2E Tests', () => {
  let app: INestApplication;
  let transactionManager: TransactionManager;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [
        MaterialMngModule,
        TransactionalTestModule, // 自定义事务测试模块
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    transactionManager = app.get(TransactionManager);
    await app.init();
  });

  beforeEach(async () => {
    await transactionManager.startTransaction();
  });

  afterEach(async () => {
    await transactionManager.rollbackTransaction();
  });

  it('should create and query snip in transaction', async () => {
    const response = await request(app.getHttpServer())
      .post('/api/v1/snip/create')
      .send({
        title: 'Test Article',
        content: 'Test Content',
      })
      .expect(201);

    // 数据在事务中可见，但不会提交到数据库
  });
});
```

## 方案五：使用数据库级别的保存点（Savepoints）

```typescript
class SavepointTestHelper {
  private savepoints: string[] = [];

  async createSavepoint(db: any, name: string): Promise<void> {
    await db.execute(sql`SAVEPOINT ${sql.identifier(name)}`);
    this.savepoints.push(name);
  }

  async rollbackToSavepoint(db: any, name: string): Promise<void> {
    await db.execute(sql`ROLLBACK TO SAVEPOINT ${sql.identifier(name)}`);
  }

  async releaseAllSavepoints(db: any): Promise<void> {
    for (const savepoint of this.savepoints.reverse()) {
      await db.execute(sql`RELEASE SAVEPOINT ${sql.identifier(savepoint)}`);
    }
    this.savepoints = [];
  }
}

// 使用示例
describe('Complex Transaction Tests', () => {
  const savepointHelper = new SavepointTestHelper();

  it('should test with nested savepoints', async () => {
    await db.transaction(async (trx) => {
      // 创建主保存点
      await savepointHelper.createSavepoint(trx, 'main_test');
      
      // 执行一些操作
      await snipRepository.save(snip1, trx);
      
      // 创建嵌套保存点
      await savepointHelper.createSavepoint(trx, 'nested_test');
      
      // 执行更多操作
      await boardRepository.save(board, trx);
      
      // 可以选择性回滚到特定保存点
      await savepointHelper.rollbackToSavepoint(trx, 'nested_test');
      
      // 最终回滚整个事务
      throw new Error('ROLLBACK_ALL');
    });
  });
});
```

## 推荐的最佳实践

1. **使用方案一**作为默认选择，它简单且易于理解
2. **集成测试**使用独立的测试数据库，每次测试后回滚
3. **单元测试**尽量使用 Mock，避免真实数据库操作
4. **性能测试**可以使用方案五的保存点机制，更灵活地控制事务

## 实际应用示例

```typescript
// 在 material-mng 模块的测试中使用
describe('CreateArticleHandler', () => {
  let handler: CreateArticleHandler;
  let trx: any;

  beforeEach(async () => {
    // 开始事务
    trx = await db.transaction();
    
    // Mock 所有 repository 使用事务
    jest.spyOn(snipRepository, 'save').mockImplementation(
      (entity, transaction) => snipRepository.save(entity, transaction || trx)
    );
  });

  afterEach(async () => {
    // 回滚所有更改
    await trx.rollback();
  });

  it('should create article and update board without persisting', async () => {
    const command = new CreateArticleCommand({
      spaceId: 'test-space',
      creatorId: 'test-user',
      title: 'Test Article',
      boardId: 'test-board',
    });

    const result = await handler.execute(command);
    
    expect(result).toBeDefined();
    // 所有数据库更改都会被回滚
  });
});
```

这种方式确保：
- 测试之间完全隔离
- 不需要手动清理数据
- 可以测试复杂的事务场景
- 提高测试运行速度