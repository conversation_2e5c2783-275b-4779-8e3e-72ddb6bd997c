import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { type CookieOptions, createServerClient } from '@supabase/ssr';
import { Request, Response } from 'express';

@Injectable()
export class SupabaseService {
  private readonly logger = new Logger(SupabaseService.name);

  constructor(private readonly configService: ConfigService) {}

  createClient(request: Request, response: Response) {
    return createServerClient(
      this.configService.getOrThrow<string>('NEXT_PUBLIC_SUPABASE_URL'),
      this.configService.getOrThrow<string>('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
      {
        cookies: {
          get: (name: string) => {
            return request.cookies?.[name];
          },
          set: (name: string, value: string, options: CookieOptions) => {
            if (response.headersSent) {
              this.logger.warn(
                `[SupabaseService] headersSent, cannot set cookie: ${name}, ${value}, ${JSON.stringify(options)}`,
              );
            } else {
              this.logger.debug(
                `[SupabaseService] setCookie: ${name}, ${value}, ${JSON.stringify(options)}`,
              );
              response.cookie(name, value, options);
            }
          },
          remove: (name: string) => {
            if (response.headersSent) {
              this.logger.warn(`[SupabaseService] headersSent, cannot remove cookie: ${name}`);
            } else {
              this.logger.debug(`[SupabaseService] removeCookie: ${name}`);
              response.clearCookie(name);
            }
          },
        },
      },
    );
  }

  createAdminClient(request?: Request, response?: Response) {
    return createServerClient(
      this.configService.getOrThrow<string>('NEXT_PUBLIC_SUPABASE_URL'),
      this.configService.getOrThrow<string>('SUPABASE_SERVICE_KEY'),
      {
        cookies: {
          get: (name: string) => {
            return request?.cookies?.[name];
          },
          set: (name: string, value: string, options: CookieOptions) => {
            this.logger.debug(
              `[SupabaseService] Admin setCookie: ${name}, ${value}, ${JSON.stringify(options)}`,
            );
            response?.cookie(name, value, options);
          },
          remove: (name: string) => {
            this.logger.debug(`[SupabaseService] Admin removeCookie: ${name}`);
            response?.clearCookie(name);
          },
        },
      },
    );
  }
}
