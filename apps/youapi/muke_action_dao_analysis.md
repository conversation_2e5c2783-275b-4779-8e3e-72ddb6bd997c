# ActionDAO 使用分析（modules 目录范围）

## 概述

ActionDAO 是一个用于管理 "actions" 表的数据访问对象，原本用于定义和管理 Snip 内容块（Block）的操作类型。

**重要发现**：Block 中的 actionId 字段已经废弃，相关的 ActionDAO 使用可以完全移除。

**分析范围**：仅限于 `src/modules` 目录下的使用情况。

## 数据库表结构

actions 表定义（位于 `src/dao/db/public.schema.ts`）：
```typescript
export const actions = pgTable('actions', {
  id: uuid('id').primaryKey().$default(() => uuidv7()),
  created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  deleted_at: timestamp('deleted_at', { withTimezone: true }),
  name: varchar('name', { length: 255 }).notNull().unique(),
  description: varchar('description', { length: 2048 }),
  // ... 其他字段
});
```

## ActionDAO 在 modules 中的使用位置

### 唯一使用位置：material-mng 模块

#### 文件：`src/modules/material-mng/services/handlers/snip/upsert-snip-blocks.handler.ts`

**使用场景**：创建或更新 Snip 的内容块（Block）

**具体使用**：
1. **创建 transcript 块时**（第82行）：
   ```typescript
   const transcriptAction = await this.actionDAO.selectOneByName('transcript');
   ```
   - 用于获取 `transcript` action 的 ID
   - 创建 Transcript 类型的 Block 时需要关联此 action

2. **创建 overview 块时**（第161行）：
   ```typescript
   const overviewAction = await this.actionDAO.selectOneByName('overview');
   ```
   - 用于获取 `overview` action 的 ID
   - 创建 Overview 类型的 Block 时需要关联此 action

**业务逻辑**：
- 每个 Block（内容块）都需要关联一个 Action，表示这个块的操作类型
- Transcript 块用于存储转录内容（如视频/音频的字幕）
- Overview 块用于存储概览内容（如文章摘要、AI 生成的总结）


## ActionDAO 方法分析

### 主要方法

1. **selectOneByName(name: string)**
   - 根据 action 名称查询单个 action 记录
   - 是最常用的方法，用于获取特定类型的 action

2. **getSnipActions()**
   - 获取所有 actions 并按 Snip 类型分组
   - 定义了哪些 Snip 类型支持哪些 actions：
     - ARTICLE、PDF、OFFICE、TEXT_FILE：支持 `overview`
     - VOICE、VIDEO：支持 `overview` 和 `transcript`
     - 其他类型：不支持任何 action

## 问题分析

### 1. 直接使用 DAO 的问题

- **违反 DDD 原则**：在 Handler 中直接注入和使用 ActionDAO，绕过了 Domain 层
- **缺少业务逻辑封装**：获取 action 的逻辑没有统一的业务服务

### 2. 硬编码问题

- Action 名称（'transcript'、'overview'）在代码中硬编码
- 没有常量定义，容易出现拼写错误

### 3. 模块内使用情况

- 在 modules 目录下，ActionDAO 仅在 material-mng 模块的一个 Handler 中使用
- 这是一个相对独立的使用场景，便于重构

## 重构建议

### 1. 创建 ActionDomainService

```typescript
@Injectable()
export class ActionDomainService {
  constructor(private readonly actionDAO: ActionDAO) {}

  async getTranscriptAction(): Promise<ActionDO> {
    const action = await this.actionDAO.selectOneByName('transcript');
    if (!action) {
      throw new Error('Transcript action not found');
    }
    return action;
  }

  async getOverviewAction(): Promise<ActionDO> {
    const action = await this.actionDAO.selectOneByName('overview');
    if (!action) {
      throw new Error('Overview action not found');
    }
    return action;
  }

  async getSnipActions() {
    return this.actionDAO.getSnipActions();
  }
}
```

### 2. 定义 Action 常量

```typescript
export enum ActionType {
  TRANSCRIPT = 'transcript',
  OVERVIEW = 'overview',
}
```

### 3. 修改使用方式

在 Handler 中注入 ActionDomainService 而不是 ActionDAO：
```typescript
constructor(
  private readonly actionDomainService: ActionDomainService,
  // ... 其他依赖
) {}

// 使用时
const transcriptAction = await this.actionDomainService.getTranscriptAction();
```

## 业务逻辑分析

### UpsertSnipBlocksHandler 的核心逻辑

1. **处理 Transcript 块**：
   - 获取名为 'transcript' 的 action
   - 一个 Snip 只有一个 transcript block
   - 支持多种格式的 content（如 SUBTITLE）
   - 如果 block 已存在则更新，不存在则创建

2. **处理 Overview 块**：
   - 获取名为 'overview' 的 action
   - 一个 Snip 只有一个 overview block
   - 只支持一个 content（LLM_OUTPUT 格式）
   - 如果 block 已存在则更新，不存在则创建

3. **Action 的作用**：
   - 作为 Block 的类型标识
   - 关联到 block 表的 action_id 字段
   - 用于区分不同类型的内容块

## 清理建议

既然 Block 中的 actionId 已经废弃，建议进行以下清理：

### 1. 移除 ActionDAO 的使用

在 `upsert-snip-blocks.handler.ts` 中：
- 移除 ActionDAO 的导入和注入
- 移除获取 transcript action 的代码（第82-85行）
- 移除获取 overview action 的代码（第161-164行）
- 移除创建 Block 时传递的 actionId 参数

### 2. 简化代码

```typescript
// 原代码
const transcriptAction = await this.actionDAO.selectOneByName('transcript');
if (!transcriptAction) {
  throw new BadRequestException('Transcript action not found');
}
transcript = Transcript.create({
  snipId,
  display: BlockDisplayEnum.SHOW,
  actionId: transcriptAction.id,  // 移除这行
});

// 简化后
transcript = Transcript.create({
  snipId,
  display: BlockDisplayEnum.SHOW,
});
```

### 3. 检查 Block 实体

确认 Block 实体中：
- actionId 字段是否已标记为废弃
- create 方法是否还需要 actionId 参数

## 总结

在 modules 目录范围内：
1. **ActionDAO 仅在 material-mng 模块的一个 Handler 中使用**
2. **由于 actionId 已废弃，这些使用都可以安全移除**
3. **清理后的好处**：
   - 减少不必要的数据库查询
   - 简化代码逻辑
   - 消除对 DAO 层的直接依赖
   - 移除硬编码的 action 名称

4. **行动计划**：
   - 从 UpsertSnipBlocksHandler 中移除所有 ActionDAO 相关代码
   - 更新 Block 创建逻辑，不再传递 actionId
   - 考虑是否需要在数据库层面清理 actions 表（如果完全不再使用）