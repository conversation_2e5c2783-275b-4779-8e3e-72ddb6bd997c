# Material-MNG 模块对 src/domain 和 src/dao 的依赖分析

## 概览

Material-MNG 模块使用了大量的 domain 服务，但对 dao 的直接依赖相对较少。这说明该模块遵循了 DDD 架构模式，主要通过 domain 层来处理业务逻辑。

## Domain 依赖统计（排除 FileDomainService）

### 使用频率统计

| Domain 模块 | 使用次数 | 主要用途 | material-mng 中有对应 entity |
|------------|---------|---------|---------------------------|
| @/domain/usage-record | 12 | 使用记录服务 | ❌ |
| @/domain/space | 9 | 空间管理服务 | ❌ |
| @/domain/usage-record/types | 4 | 使用记录类型定义 | - |
| @/domain/snip | 3 | Snip 相关服务 | ✅ (snip.entity.ts 及多个子类型) |
| @/domain/user | 2 | 用户相关服务 | ❌ |
| @/domain/subscription/types | 2 | 订阅类型定义 | - |
| @/domain/content | 2 | 内容管理服务 | ❌ |
| @/domain/block | 2 | 块管理服务 | ✅ (block.entity.ts, block-content.entity.ts) |
| @/domain/domain.module | 1 | Domain 模块导入 | - |
| @/domain/content/types | 1 | 内容类型定义 | - |
| @/domain/chat/tool_call/image-generate.service | 1 | 图片生成服务 | ❌ |
| @/domain/chat/context/builder | 1 | 聊天上下文构建器 | ❌ |
| @/domain/board | 1 | Board 服务 | ✅ (board.entity.ts) |
| @/domain/board-item | 1 | Board Item 服务 | ✅ (board-item.entity.ts) |
| @/domain/board-group | 1 | Board Group 服务 | ✅ (board-group.entity.ts) |

注：FileDomainService (@/domain/file) 和相关的 @/domain/file/types 已按要求忽略。

### 具体使用位置

#### 1. UsageRecordDomainService (@/domain/usage-record) - 12处使用

- `services/handlers/webhook/webhook-fetched.handler.ts`
- `services/handlers/webhook/webhook-images-transfered.handler.ts`
- `services/handlers/webhook/webhook-subtitle-transcribed.handler.ts`
- `services/handlers/usage-record/create-storage-usage-record-from-editor.handler.ts`
- `services/handlers/snip/create-other-webpage.handler.ts`
- `services/handlers/snip/create-pdf.handler.ts`
- `services/handlers/snip/try-create-snip-by-url.handler.ts`
- `services/handlers/snip/create-voice.handler.ts`
- `services/handlers/snip/create-text-file.handler.ts`
- `services/handlers/snip/create-office.handler.ts`
- `services/handlers/snip/create-image.handler.ts`
- `services/handlers/file/gen-signed-put-url-if-not-exist.handler.ts`

#### 2. SpaceDomainService (@/domain/space) - 9处使用

- `services/handlers/snip/create-other-webpage.handler.ts`
- `services/handlers/snip/create-pdf.handler.ts`
- `services/handlers/snip/try-create-snip-by-url.handler.ts`
- `services/handlers/snip/create-voice.handler.ts`
- `services/handlers/snip/create-text-file.handler.ts`
- `services/handlers/snip/create-office.handler.ts`
- `services/handlers/snip/create-image.handler.ts`
- `services/handlers/snip/edit-image.handler.ts`
- `services/handlers/file/gen-signed-put-url-if-not-exist.handler.ts`

#### 3. 其他 Domain 服务

- **SnipDomainService**: 在转录说话人更新、检测说话人、添加标点等功能中使用
- **ContentDomainService**: 在转录说话人更新和添加标点功能中使用
- **BlockDomainService**: 在添加标点和克隆 snip 功能中使用
- **UserDomainService**: 在生成图片信息和调用概览功能中使用
- **BoardDomainService**: 在尝试通过 URL 创建 snip 时使用
- **BoardItemDomainService**: 在移动实体到未分类时使用
- **BoardGroupDomainService**: 在创建图片时使用

## DAO 依赖统计

### 使用频率统计

| DAO 模块 | 使用次数 | 主要用途 |
|---------|---------|---------|
| @/dao/usage-record/types | 2 | StorageUsageFromEnum 类型 |
| @/dao/dao.module | 1 | DAO 模块导入 |
| @/dao/content/types | 1 | InsertBlockContentParam 类型 |
| @/dao/action | 1 | ActionDAO |

### 具体使用位置

1. **StorageUsageFromEnum** (@/dao/usage-record/types)
   - `services/handlers/snip/create-voice.handler.ts`
   - `services/handlers/snip/create-image.handler.ts`

2. **DaoModule** (@/dao/dao.module)
   - `material-mng.module.ts`

3. **InsertBlockContentParam** (@/dao/content/types)
   - `services/handlers/snip/add-punctuation.handler.ts`

4. **ActionDAO** (@/dao/action)
   - `services/handlers/snip/upsert-snip-blocks.handler.ts`

## Material-MNG 模块中的 Entity 分析

### 在 material-mng 中有对应 entity 的 DomainService：

1. **SnipDomainService** - 完整的 entity 体系
   - snip.entity.ts (基类)
   - article.entity.ts
   - image.entity.ts
   - office.entity.ts
   - other-webpage.entity.ts
   - pdf.entity.ts
   - snippet.entity.ts
   - text-file.entity.ts
   - unknown-webpage.entity.ts
   - video.entity.ts
   - voice.entity.ts

2. **BlockDomainService** - 块相关 entity
   - block.entity.ts
   - block-content.entity.ts
   - overview.entity.ts
   - transcript.entity.ts

3. **Board 相关服务** - Board 体系 entity
   - board.entity.ts (BoardDomainService)
   - board-item.entity.ts (BoardItemDomainService)
   - board-group.entity.ts (BoardGroupDomainService)

### 在 material-mng 中没有对应 entity 的 DomainService：

以下 DomainService 的 entity 都在 **iam 模块**中定义：
1. **UsageRecordDomainService** (12处使用) - 对应 iam/domain/usage/models/usage-record.entity.ts
2. **SpaceDomainService** (9处使用) - 对应 iam/domain/space/models/space.entity.ts
3. **UserDomainService** (2处使用) - 对应 iam/domain/user/models/user.entity.ts

其他跨模块依赖：
4. **ContentDomainService** (2处使用) - entity 位置待确认
5. **Chat 相关服务**
   - ImageGenerateService - AI 相关服务，可能无 entity
   - ContextBuilder - AI 相关服务，可能无 entity

### 其他在 material-mng 中定义但未被引用为 DomainService 的 entity：

- favorite.entity.ts
- note.entity.ts
- playlist-item.entity.ts
- short-link.entity.ts
- thought.entity.ts
- thought-version.entity.ts
- diff-review-event.entity.ts

这些 entity 可能是通过 Repository 模式直接使用，而非通过 DomainService。

## IAM 模块 Entity 概览

IAM 模块包含以下核心 entity：
- **user.entity.ts** - 用户实体
- **space.entity.ts** - 空间（工作空间）实体
- **usage-record.entity.ts** - 使用记录实体
- **credit-account.entity.ts** - 积分账户实体
- **credit-transaction.entity.ts** - 积分交易实体
- **customer.entity.ts** - 客户实体
- **subscription.entity.ts** - 订阅实体

这解释了为什么 material-mng 模块频繁使用来自 iam 的 DomainService，因为：
- 需要检查用户权限（UserDomainService）
- 需要验证空间权限（SpaceDomainService）
- 需要记录资源使用情况（UsageRecordDomainService）

## 分析结论

### 1. 架构遵循情况

Material-MNG 模块严格遵循了 DDD 架构原则：
- 主要通过 Domain 层服务处理业务逻辑
- DAO 层的直接使用极少，仅在必要时使用
- 类型定义主要从 Domain 层导入

### 2. 核心依赖（排除 FileDomainService）

最核心的依赖是：
- **UsageRecordDomainService**: 记录使用情况（12处使用）- 🔗 entity 在 iam 模块
- **SpaceDomainService**: 处理空间权限和管理（9处使用）- 🔗 entity 在 iam 模块
- **SnipDomainService**: 处理 Snip 相关业务逻辑（3处使用）- ✅ 有完整的本地 entity 体系

### 3. 模块边界问题

从 entity 分析可以看出：
- **合理的跨模块依赖**：最常用的 UsageRecordDomainService、SpaceDomainService 和 UserDomainService 的 entity 都在 iam 模块中，这是合理的架构设计，因为这些是用户身份和权限相关的核心功能
- **本地 entity 利用不充分**：有7个 entity（如 favorite、note、thought 等）定义在本模块但没有对应的 DomainService 被使用，可能是通过 Repository 模式直接访问

### 4. 清理建议

对于 DAO 层的使用：
1. `StorageUsageFromEnum` 在两个地方被使用，但在其他地方使用的是 `@/domain/usage-record/types`，建议统一使用 domain 层的类型定义
2. `InsertBlockContentParam` 和 `ActionDAO` 的直接使用可以考虑是否能通过 Domain 服务封装

### 5. 模块边界

Material-MNG 模块的边界清晰：
- 主要处理材料管理相关的业务逻辑
- 通过 Domain 服务与其他模块交互
- 对底层 DAO 的依赖最小化

## 需要注意的文件

以下文件直接使用了 DAO：
1. `services/handlers/snip/add-punctuation.handler.ts` - 使用了 `InsertBlockContentParam`
2. `services/handlers/snip/upsert-snip-blocks.handler.ts` - 使用了 `ActionDAO`
3. `services/handlers/snip/create-voice.handler.ts` - 使用了 `StorageUsageFromEnum` (dao版本)
4. `services/handlers/snip/create-image.handler.ts` - 使用了 `StorageUsageFromEnum` (dao版本)

这些文件在后续重构时可能需要特别关注。