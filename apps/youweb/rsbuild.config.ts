import path from 'node:path';
import { getFavicon } from '@repo/ui/lib/get-favicon';
import { defineConfig } from '@rsbuild/core';
import { pluginLess } from '@rsbuild/plugin-less';
import { pluginNodePolyfill } from '@rsbuild/plugin-node-polyfill';
import { pluginReact } from '@rsbuild/plugin-react';
import { pluginSass } from '@rsbuild/plugin-sass';
import { RsdoctorRspackPlugin } from '@rsdoctor/rspack-plugin';
import { pluginHtmlMinifierTerser } from 'rsbuild-plugin-html-minifier-terser';
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';
import appConfig from './scripts/get-config';
import getEnv from './scripts/get-env';
import { scriptInjector } from './scripts/inject-user';
// import { fetUserMiddleWare, scriptInjector } from './scripts/inject-user';
import { getDevServerProxyConfig } from './scripts/proxy';

// 入口配置
const entries = {
  // 主入口
  index: './src/index.tsx',
  // 统一移动端入口
  mobile: './src/thought-mobile/mobile.entry.tsx',
  // 旧版移动端入口（待废弃）
  mobileThought: './src/thought-mobile/old-thought.entry.tsx',
  mobileEditor: './src/thought-mobile/old-editor.entry.tsx',
} as const;

// 支持通过 ONLY_ENTRY 环境变量指定单一入口构建
const onlyEntry = process.env.ONLY_ENTRY as string | undefined;
const finalEntries =
  onlyEntry && Object.hasOwn(entries, onlyEntry)
    ? { [onlyEntry]: entries[onlyEntry as keyof typeof entries] }
    : entries;

export default defineConfig({
  resolve: {
    alias: {
      '@components': './src/components',
      '@repo/common': '../../packages/common/src',
      '@repo/editor-common': '../../packages/editor-common/src',
      '@repo/ui-business-editor': '../../packages/ui-business-editor/src',
      '@repo/ui-business-snip': '../../packages/ui-business-snip/src',
      react: path.resolve('./node_modules/react'),
    },
  },
  tools: {
    rspack: {
      module: {
        rules: [
          {
            test: /\.md$/,
            type: 'asset/source',
          },
        ],
      },
      plugins: [
        ...(process.env.ANALYZE === '1' ? [new RsdoctorRspackPlugin()] : []),
        ...(process.env.BUNDLE_ANALYZE === '1'
          ? [
              new BundleAnalyzerPlugin({
                analyzerMode: 'static',
                openAnalyzer: true,
                generateStatsFile: true,
                reportFilename: 'bundle-report.html',
                statsFilename: 'stats.json',
                defaultSizes: 'parsed',
              }),
            ]
          : []),
      ],
    },
  },
  plugins: [
    pluginReact(),
    pluginLess(),
    pluginSass(),
    pluginHtmlMinifierTerser(),
    pluginNodePolyfill(),
  ],
  server: {
    publicDir: false,
    port: 2000,
    historyApiFallback: {
      rewrites: [
        // 统一移动端路由
        { from: /^\/mobile\//, to: '/mobile.html' },
        // 旧版移动端路由（待废弃）
        { from: /^\/mobileThought/, to: '/mobile-old-thought.html' },
        { from: /^\/mobileEditor/, to: '/mobile-old-editor.html' },
        // 默认回退路由
        { from: /./, to: '/index.html' },
      ],
    },
    proxy: getDevServerProxyConfig(),
  },
  source: {
    entry: finalEntries,
    define: {
      'process.env': JSON.stringify(getEnv()),
    },
    transformImport: [
      {
        libraryName: 'lodash',
        customName: 'lodash/{{ member }}',
      },
    ],
  },
  dev: {
    setupMiddlewares: [
      (middlewares) => {
        middlewares.unshift(
          scriptInjector([
            `<script>window.__APP_CONFIG__ = ${JSON.stringify(appConfig)};</script>`,
          ]),
        );
        // middlewares.unshift(fetUserMiddleWare);
      },
    ],
  },
  html: {
    template: './scripts/index.html',
    ...calcHTMLConfig(),
  },
  output: {
    copy: [
      // `./src/assets/image.png` -> `./static/assets/image.png`
      { from: './public/assets', to: 'static/assets' },
      { from: './public/fonts', to: 'static/fonts' },
    ],
    sourceMap: {
      js: process.env.NODE_ENV === 'production' ? 'hidden-source-map' : 'cheap-module-source-map',
      css: true,
    },
  },
});

function calcHTMLConfig() {
  const env = process.env.YOUMIND_ENV || 'development';
  const isDev = process.env.NODE_ENV === 'development';

  if (isDev) {
    return {
      favicon: getFavicon('development'),
    };
  }

  // 生产环境和预览环境使用 tags 而非 favicon
  return {
    tags: [
      {
        tag: 'link',
        attrs: {
          rel: 'icon',
          href: getFavicon(env === 'preview' ? 'preview' : 'production'),
        },
      },
    ],
  };
}
