import React from 'react';
import ReactDOM from 'react-dom/client';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { ThoughtMobileLayout } from './thought-mobile-layout';

// 统一的移动端入口，处理所有 /mobile/* 路由
const router = createBrowserRouter(
  [
    {
      path: '/',
      Component: ThoughtMobileLayout,
      children: [
        {
          path: 'thought-detail',
          lazy: async () => {
            const { MobileThoughtBodyApp } = await import('../components/thought/mobile/mobile-thought-body-app');
            return { Component: MobileThoughtBodyApp };
          },
        },
        {
          path: 'thought-editor',
          lazy: async () => {
            const { MobileEditorPage } = await import('../pages/Mobile-Editor');
            return { Component: MobileEditorPage };
          },
        },
      ],
    },
  ],
  {
    basename: '/mobile',
  },
);

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>,
);
