import React from 'react';
import ReactDOM from 'react-dom/client';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { ThoughtMobileLayout } from './thought-mobile-layout';

// 统一的移动端入口，处理所有 /mobile/* 路由
const router = createBrowserRouter([
  {
    path: '/mobile/thought-detail',
    element: (
      <ThoughtMobileLayout>
        <React.Suspense fallback={<div>Loading...</div>}>
          {React.createElement(React.lazy(() => import('../components/thought/mobile/mobile-thought-body-app').then(m => ({ default: m.MobileThoughtBodyApp }))))}
        </React.Suspense>
      </ThoughtMobileLayout>
    ),
  },
  {
    path: '/mobile/thought-editor',
    element: (
      <ThoughtMobileLayout>
        <React.Suspense fallback={<div>Loading...</div>}>
          {React.createElement(React.lazy(() => import('../pages/Mobile-Editor').then(m => ({ default: m.MobileEditorPage }))))}
        </React.Suspense>
      </ThoughtMobileLayout>
    ),
  },
]);

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>,
);
