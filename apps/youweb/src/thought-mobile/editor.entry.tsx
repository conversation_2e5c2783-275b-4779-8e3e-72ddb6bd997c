import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { MobileEditorPage } from '../pages/Mobile-Editor';
import { ThoughtMobileLayout } from './thought-mobile-layout';

// 地址：'/mobile/thought-editor'
const router = createBrowserRouter(
  [
    {
      path: '/',
      Component: ThoughtMobileLayout,
      children: [
        {
          index: true,
          Component: MobileEditorPage,
        },
      ],
    },
  ],
  {
    basename: '/mobile/thought-editor',
  },
);

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>,
);
