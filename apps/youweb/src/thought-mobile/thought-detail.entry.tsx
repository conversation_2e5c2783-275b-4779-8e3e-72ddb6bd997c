import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { MobileThoughtBodyApp } from '../components/thought/mobile/mobile-thought-body-app';
import { ThoughtMobileLayout } from './thought-mobile-layout';

// 地址：'/mobile/thought-detail'
const router = createBrowserRouter(
  [
    {
      path: '/',
      Component: ThoughtMobileLayout,
      children: [
        {
          index: true,
          Component: MobileThoughtBodyApp,
        },
      ],
    },
  ],
  {
    basename: '/mobile/thought-detail',
  },
);

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>,
);
